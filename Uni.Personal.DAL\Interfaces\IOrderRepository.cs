using System;
using System.Threading.Tasks;
using UNI.Common.CommonBase;
using UNI.Model;
using Uni.Personal.Model;

namespace Uni.Personal.DAL.Interfaces
{
    /// <summary>
    /// Order repository interface
    /// </summary>
    public interface IOrderRepository : IBaseRepository<OrderFilterInput>
    {
        // Inherits all methods from IBaseRepository:
        // - Task<CommonListPage> GetPageAsync(OrderFilterInput filter);
        // - Task<CommonViewOidInfo> GetInfoAsync(Guid? oid);
        // - Task<BaseValidate<Guid?>> SetInfoAsync(CommonViewOidInfo info);
        // - Task<BaseValidate> DeleteAsync(Guid? oid);

        /// <summary>
        /// Get detailed order information including items
        /// </summary>
        /// <param name="oid">Order ID</param>
        /// <returns>Detailed order information</returns>
        Task<OrderDetailView?> GetOrderDetailAsync(Guid? oid);

        /// <summary>
        /// Update order status
        /// </summary>
        /// <param name="oid">Order ID</param>
        /// <param name="status">New status</param>
        /// <returns>Validation result</returns>
        Task<BaseValidate> UpdateOrderStatusAsync(Guid? oid, int status);
    }
}
